# 前端集成指南

## 概述

本指南说明如何在前端项目中集成用户登录系统的各项功能，包括认证、平台账号管理、SSE推送等。

## 基础配置

### 1. API基础URL配置
```javascript
const API_URL = window.location.origin; // 或指定的API服务器地址
```

### 2. 会话管理
```javascript
let sessionId = null;

// 保存会话
function saveSession(sessionId) {
  localStorage.setItem('sessionId', sessionId);
}

// 获取会话
function getSession() {
  return localStorage.getItem('sessionId');
}

// 清除会话
function clearSession() {
  localStorage.removeItem('sessionId');
  sessionId = null;
}
```

## 用户认证

### 1. 登录实现
```javascript
async function login(phone, password, clientVersion = '1.0.0') {
  try {
    const response = await fetch(`${API_URL}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ phone, password, clientVersion })
    });
    
    const data = await response.json();
    
    if (data.success) {
      sessionId = data.sessionId;
      saveSession(sessionId);
      return { success: true, user: data.user };
    } else if (data.requireForceLogin) {
      return { success: false, requireForceLogin: true };
    } else if (data.needUpgrade) {
      return { success: false, needUpgrade: true, latestVersion: data.latestVersion };
    } else {
      return { success: false, message: data.message };
    }
  } catch (error) {
    console.error('登录失败:', error);
    return { success: false, message: '网络错误' };
  }
}
```

### 2. 强制登录
```javascript
async function forceLogin(phone, password, clientVersion = '1.0.0') {
  try {
    const response = await fetch(`${API_URL}/auth/login`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        phone, 
        password, 
        clientVersion,
        forceLogin: true 
      })
    });
    
    const data = await response.json();
    
    if (data.success) {
      sessionId = data.sessionId;
      saveSession(sessionId);
      return { success: true, user: data.user };
    }
    
    return { success: false, message: data.message };
  } catch (error) {
    console.error('强制登录失败:', error);
    return { success: false, message: '网络错误' };
  }
}
```

### 3. 会话检查
```javascript
async function checkSession() {
  const savedSessionId = getSession();
  if (!savedSessionId) return false;
  
  try {
    const response = await fetch(`${API_URL}/auth/check-session`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ sessionId: savedSessionId })
    });
    
    const data = await response.json();
    
    if (data.success) {
      sessionId = savedSessionId;
      return { success: true, userId: data.userId, phone: data.phone };
    } else {
      clearSession();
      return false;
    }
  } catch (error) {
    console.error('会话检查失败:', error);
    clearSession();
    return false;
  }
}
```

### 4. 退出登录
```javascript
async function logout() {
  const currentSessionId = sessionId;
  clearSession();
  
  if (currentSessionId) {
    try {
      await fetch(`${API_URL}/auth/logout`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sessionId: currentSessionId })
      });
    } catch (error) {
      console.error('退出登录失败:', error);
    }
  }
}
```

## 平台账号管理

### 1. 添加平台账号
```javascript
async function addPlatformAccount(accountData) {
  try {
    const response = await fetch(`${API_URL}/api/user/platform-accounts`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        account_data: {
          phone: accountData.phone,
          platform: accountData.platform,
          login_type: accountData.contentType,
          sessionid: accountData.sessionid,
          team_tag: '-',
          username: '-',
          homepage_url: '-',
          is_verified: '否',
          drafts_count: '-',
          account_status: '正常',
          data_update_time: new Date().toISOString().replace('T', ' ').substring(0, 19),
          login_time: new Date().toISOString().replace('T', ' ').substring(0, 19),
          stats: {
            followers: '-',
            total_reads: '-',
            total_income: '-',
            yesterday_reads: '-',
            yesterday_income: '-',
            credit_score: '-',
            can_withdraw_amount: '-'
          }
        },
        owner_id: currentUser.id,
        current_holder_id: currentUser.id,
        sessionId: sessionId
      })
    });
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('添加平台账号失败:', error);
    return { success: false, message: '网络错误' };
  }
}
```

### 2. 获取平台账号列表
```javascript
async function loadPlatformAccounts(userId, isMainAccount) {
  try {
    const response = await fetch(
      `${API_URL}/api/user/platform-accounts?user_id=${userId}&is_main_account=${isMainAccount}&sessionId=${sessionId}`
    );
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('获取平台账号失败:', error);
    return { success: false, message: '网络错误' };
  }
}
```

### 3. 转移平台账号
```javascript
async function transferPlatformAccount(phone, newHolderId, userId) {
  try {
    const response = await fetch(`${API_URL}/api/user/transfer-platform-account`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        phone: phone,
        new_holder_id: newHolderId,
        user_id: userId,
        sessionId: sessionId
      })
    });
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('转移平台账号失败:', error);
    return { success: false, message: '网络错误' };
  }
}
```

### 4. 批量转移平台账号
```javascript
async function batchTransferPlatformAccounts(userId, transfers) {
  try {
    const response = await fetch(`${API_URL}/api/user/batch-transfer-platform-accounts`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        user_id: userId,
        transfers: transfers,
        sessionId: sessionId
      })
    });
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('批量转移平台账号失败:', error);
    return { success: false, message: '网络错误' };
  }
}
```

## SSE实时推送

### 1. 建立SSE连接
```javascript
let sseConnection = null;

function connectSSE() {
  if (sseConnection && sseConnection.readyState === EventSource.OPEN) {
    return; // 已经连接
  }
  
  if (!sessionId) return;
  
  const clientId = localStorage.getItem('clientId') || generateClientId();
  localStorage.setItem('clientId', clientId);
  
  const sseUrl = `${API_URL}/auth/sse?sessionId=${sessionId}&clientId=${clientId}`;
  
  sseConnection = new EventSource(sseUrl);
  
  sseConnection.onopen = () => {
    console.log('SSE连接已建立');
  };
  
  sseConnection.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data);
      handleSSEMessage(data);
    } catch (error) {
      console.error('处理SSE消息时出错:', error);
    }
  };
  
  sseConnection.onerror = (error) => {
    console.error('SSE错误:', error);
    // EventSource会自动重连
  };
}

function generateClientId() {
  return 'client_' + Math.random().toString(36).substring(2, 15);
}
```

### 2. 处理SSE消息
```javascript
function handleSSEMessage(data) {
  switch (data.type) {
    case 'force_logout':
      showMessage(data.message, 'error');
      logout();
      break;
      
    case 'connected':
      console.log('SSE连接确认');
      break;
      
    case 'sub_account_platform_added':
      showMessage(data.message, 'info');
      setTimeout(() => {
        loadPlatformAccounts();
      }, 1000);
      break;
      
    case 'platform_account_received':
      const messageType = data.data && data.data.addedByUserType === '管理员' ? 'info' : 'success';
      showMessage(data.message, messageType);
      setTimeout(() => {
        loadPlatformAccounts();
      }, 1000);
      break;
      
    case 'platform_accounts_batch_received':
      const batchMessageType = data.data && data.data.operatorType === '管理员' ? 'info' : 'success';
      showMessage(data.message, batchMessageType);
      setTimeout(() => {
        loadPlatformAccounts();
      }, 1000);
      break;
      
    case 'platform_data_update':
      loadPlatformAccounts();
      break;
      
    case 'platform_account_deleted_notification':
      let deleteMessageType = 'warning';
      if (data.data && data.data.deletedByUserType === '管理员') {
        deleteMessageType = 'info';
      } else if (data.data && data.data.isCurrentUser) {
        deleteMessageType = 'success';
      }
      showMessage(data.message, deleteMessageType);
      setTimeout(() => {
        loadPlatformAccounts();
      }, 1000);
      break;
      
    default:
      console.log('未知SSE消息类型:', data.type);
  }
}
```

### 3. 关闭SSE连接
```javascript
function disconnectSSE() {
  if (sseConnection) {
    sseConnection.close();
    sseConnection = null;
  }
}
```

## 子账号管理

### 1. 获取子账号列表
```javascript
async function loadSubAccounts(ownerId) {
  try {
    const response = await fetch(`${API_URL}/api/user/sub-accounts?ownerId=${ownerId}&sessionId=${sessionId}`);
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('获取子账号列表失败:', error);
    return { success: false, message: '网络错误' };
  }
}
```

### 2. 创建子账号
```javascript
async function createSubAccount(ownerId, phone, password) {
  try {
    const response = await fetch(`${API_URL}/api/user/create-sub-account`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        ownerId: ownerId,
        phone: phone,
        password: password,
        sessionId: sessionId
      })
    });
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('创建子账号失败:', error);
    return { success: false, message: '网络错误' };
  }
}
```

## 工具函数

### 1. 消息显示
```javascript
function showMessage(text, type, duration = 5000) {
  // 创建消息元素
  const messageDiv = document.createElement('div');
  messageDiv.className = `message message-${type}`;
  messageDiv.textContent = text;
  
  // 添加到页面
  document.body.appendChild(messageDiv);
  
  // 自动移除
  setTimeout(() => {
    if (messageDiv.parentNode) {
      messageDiv.parentNode.removeChild(messageDiv);
    }
  }, duration);
}
```

### 2. 日期格式化
```javascript
function formatDate(date) {
  const d = new Date(date);
  return d.getFullYear() + '-' + 
         padZero(d.getMonth() + 1) + '-' + 
         padZero(d.getDate()) + ' ' + 
         padZero(d.getHours()) + ':' + 
         padZero(d.getMinutes());
}

function padZero(num) {
  return num < 10 ? '0' + num : num;
}
```

### 3. 错误处理
```javascript
function handleApiError(error, defaultMessage = '操作失败') {
  if (error.message) {
    showMessage(error.message, 'error');
  } else {
    showMessage(defaultMessage, 'error');
  }
  console.error('API错误:', error);
}
```

## 最佳实践

### 1. 错误处理
- 统一的错误处理机制
- 网络错误和业务错误分别处理
- 用户友好的错误提示

### 2. 状态管理
- 使用localStorage持久化会话
- 及时清理无效会话
- 状态变化时更新UI

### 3. 性能优化
- 避免频繁的API调用
- 使用防抖处理搜索
- 合理使用缓存

### 4. 用户体验
- 加载状态提示
- 操作确认对话框
- 实时数据更新

### 5. 安全考虑
- 敏感信息不存储在localStorage
- API请求添加CSRF保护
- 及时处理会话过期

---

*集成指南版本: v1.0*  
*最后更新: 2024-08-01*
