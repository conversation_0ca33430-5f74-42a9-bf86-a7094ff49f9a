# API接口快速参考

## 认证相关

### 登录
```http
POST /auth/login
Content-Type: application/json

{
  "phone": "***********",
  "password": "password123",
  "clientVersion": "1.0.0",
  "forceLogin": false
}
```

### 注册
```http
POST /auth/register
Content-Type: application/json

{
  "phone": "***********",
  "password": "password123"
}
```

### 会话检查
```http
POST /auth/check-session
Content-Type: application/json

{
  "sessionId": "session_abc123"
}
```

### 退出登录
```http
POST /auth/logout
Content-Type: application/json

{
  "sessionId": "session_abc123"
}
```

### 激活码
```http
POST /auth/activate-code
Content-Type: application/json

{
  "sessionId": "session_abc123",
  "code": "ACTIVATE123"
}
```

## 平台账号管理

### 添加平台账号
```http
POST /api/user/platform-accounts
Content-Type: application/json

{
  "account_data": {
    "phone": "***********",
    "platform": "头条号",
    "login_type": "视频",
    "sessionid": "platform_session_123",
    "team_tag": "-",
    "username": "-",
    "homepage_url": "-",
    "is_verified": "否",
    "drafts_count": "-",
    "account_status": "正常",
    "data_update_time": "2024-08-01 10:30:00",
    "login_time": "2024-08-01 10:30:00",
    "stats": {
      "followers": "-",
      "total_reads": "-",
      "total_income": "-",
      "yesterday_reads": "-",
      "yesterday_income": "-",
      "credit_score": "-",
      "can_withdraw_amount": "-"
    }
  },
  "owner_id": 123,
  "current_holder_id": 123,
  "sessionId": "user_session_abc123"
}
```

### 获取平台账号列表
```http
GET /api/user/platform-accounts?user_id=123&is_main_account=true&sessionId=session_abc123
```

### 编辑平台账号
```http
PUT /api/user/platform-accounts
Content-Type: application/json

{
  "phone": "***********",
  "user_id": 123,
  "is_main_account": true,
  "account_data": { /* 同添加接口的account_data */ },
  "sessionId": "session_abc123"
}
```

### 删除平台账号
```http
DELETE /api/user/platform-accounts
Content-Type: application/json

{
  "phone": "***********",
  "userId": 123,
  "sessionId": "session_abc123"
}
```

### 转移平台账号
```http
POST /api/user/transfer-platform-account
Content-Type: application/json

{
  "phone": "***********",
  "new_holder_id": 456,
  "user_id": 123,
  "sessionId": "session_abc123"
}
```

### 批量转移平台账号
```http
POST /api/user/batch-transfer-platform-accounts
Content-Type: application/json

{
  "user_id": 123,
  "transfers": [
    {
      "phone": "***********",
      "newHolderId": 456
    }
  ],
  "sessionId": "session_abc123"
}
```

## 子账号管理

### 获取子账号列表
```http
GET /api/user/sub-accounts?ownerId=123&sessionId=session_abc123
```

### 创建子账号
```http
POST /api/user/create-sub-account
Content-Type: application/json

{
  "ownerId": 123,
  "phone": "***********",
  "password": "password123",
  "sessionId": "session_abc123"
}
```

### 删除子账号
```http
POST /api/user/delete-sub-account
Content-Type: application/json

{
  "ownerId": 123,
  "subAccountId": 456,
  "sessionId": "session_abc123"
}
```

## 用户信息

### 获取用户信息
```http
GET /api/user/user-info?phone=***********&sessionId=session_abc123
```

## SSE实时推送

### 建立连接
```http
GET /auth/sse?sessionId=session_abc123&clientId=client_xyz789
```

### 消息格式
```json
{
  "type": "消息类型",
  "message": "显示消息",
  "data": {
    "附加数据": "值"
  }
}
```

### 消息类型
- `connected` - 连接确认
- `force_logout` - 强制登出
- `sub_account_platform_added` - 子账号添加平台账号
- `platform_account_received` - 平台账号转移接收
- `platform_accounts_batch_received` - 批量平台账号转移
- `platform_data_update` - 平台数据更新
- `platform_account_deleted_notification` - 平台账号删除

## 响应格式

### 成功响应
```json
{
  "success": true,
  "message": "操作成功",
  "data": { /* 具体数据 */ }
}
```

### 错误响应
```json
{
  "success": false,
  "message": "错误描述",
  "code": "ERROR_CODE"
}
```

## 常用字段说明

### 平台类型
- `头条号`
- `百家号`

### 内容类型
- `视频`
- `文章`
- `微头条`

### 账户状态
- `正常`
- `掉线`
- `异常`

### 认证状态
- `是`
- `否`

### 账号类型
- `主账号`
- `子账号`

## 错误代码

| 代码 | 说明 |
|------|------|
| AUTH_FAILED | 认证失败 |
| SESSION_EXPIRED | 会话过期 |
| PERMISSION_DENIED | 权限不足 |
| INVALID_PARAMS | 参数无效 |
| USER_NOT_FOUND | 用户不存在 |
| ACCOUNT_EXISTS | 账号已存在 |
| PLATFORM_ACCOUNT_NOT_FOUND | 平台账号不存在 |
| TRANSFER_FAILED | 转移失败 |
| ACTIVATION_FAILED | 激活失败 |

## 注意事项

1. 所有API请求都需要携带有效的`sessionId`
2. 时间格式统一使用：`YYYY-MM-DD HH:mm:ss`
3. 手机号格式：11位数字
4. SessionID为平台认证凭据，需要从平台获取
5. 批量操作有数量限制，建议单次不超过100个
6. SSE连接会自动重连，客户端无需手动处理
7. 主账号和子账号的权限不同，注意权限控制

---

*快速参考版本: v1.0*  
*最后更新: 2024-08-01*
