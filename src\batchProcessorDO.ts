﻿import { DurableObject } from 'cloudflare:workers';
import type { D1Database } from '@cloudflare/workers-types';
import {
  allDataFetcher,
  BatchProcessor,
  incomeDataFetcher,
  creditAndFansFetcher,
  accountInfoFetcher,
  accountStatusFetcher,
  readingDataFetcher,
  draftsCountFetcher
} from './dataFetcher-new';
import { FetchResult, PlatformAccountData } from './types';

/**
 * 创建标准的平台账号数据结构
 */
function createStandardAccountData(): Partial<PlatformAccountData> {
  return {
    stats: {
      followers: '0',
      total_reads: '0',
      total_income: '0',
      yesterday_reads: '0',
      yesterday_income: '0',
      credit_score: '0',
      can_withdraw_amount: '0'
    },
    is_yesterday_income_ready: false // 默认为 false
  };
}

// 批量处理进度状态
export interface BatchProgress {
  batchId: string;
  totalAccounts: number;
  processedAccounts: number;
  successCount: number;
  failureCount: number;
  currentBatch: number;
  totalBatches: number;
  status: 'waiting' | 'processing' | 'completed' | 'failed' | 'cancelled';
  startTime: number;
  lastUpdateTime: number;
  currentAccount?: string;
  results: FetchResult[];
  errorMessage?: string;
}

// 批量处理配置
export interface BatchConfig {
  batchSize: number; // 每批处理的账号数量
  delayBetweenBatches: number; // 批次间延迟（毫秒）
  delayBetweenAccounts: number; // 账号间延迟（毫秒）
}

// SSE 连接管理
interface SSEConnection {
  webSocket: WebSocket;
  batchId: string;
  connectedAt: number;
}

// 定时任务状态
interface ScheduledTaskState {
  taskId: string;
  status: 'waiting' | 'checking' | 'retrying' | 'processing' | 'batch_processing' | 'retry_processing' | 'completed' | 'failed';
  startTime: number;
  lastCheckTime: number;
  retryCount: number;
  maxRetries: number;
  retryInterval: number; // 3分钟 = 3 * 60 * 1000 毫秒
  firstAccountPhone?: string;
  firstAccountSessionId?: string;
  errorMessage?: string;
  // 智能重试相关
  currentRound: number; // 当前轮次
  maxRounds: number; // 最大轮次
  retryIntervalMinutes: number; // 重试间隔（分钟）
  notReadyAccounts: Array<{ mainAccountId: number; phone: string; sessionid: string }>; // 未准备好的账号
}

/**
 * 批量处理器 Durable Object
 * 使用 Alarms 实现分批次顺序处理，通过 SSE 推送进度
 */
// 定时任务批量处理进度
interface ScheduledBatchProgress {
  accounts: Array<{ mainAccountId: number; phone: string; sessionid: string }>;
  currentAccountIndex: number;
  totalAccounts: number;
  processedAccounts: number;
  successCount: number;
  failureCount: number;
  skippedCount: number;
  notReadyAccounts: Array<{ mainAccountId: number; phone: string; sessionid: string }>;
}

export class BatchProcessorDO extends DurableObject {
  private state: any;
  private db: D1Database;
  protected env: any;
  private currentProgress: BatchProgress | null = null;
  private accounts: Array<{ mainAccountId: number; phone: string; sessionid: string }> = [];
  private config: BatchConfig = {
    batchSize: 4,
    delayBetweenBatches: 500, // 0.5秒
    delayBetweenAccounts: 100  // 0.1秒
  };
  private scheduledTask: ScheduledTaskState | null = null;
  private scheduledBatchProgress: ScheduledBatchProgress | null = null;

  constructor(state: any, env: any) {
    super(state, env);
    this.state = state;
    this.db = env.DB;
    this.env = env;

    // 初始化时检查是否有未完成的批处理和定时任务
    this.state.blockConcurrencyWhile(async () => {
      const storedProgress = await this.state.storage.get('currentProgress') as BatchProgress;
      if (storedProgress && storedProgress.status === 'processing') {
        this.currentProgress = storedProgress;
        this.accounts = await this.state.storage.get('accounts') || [];
      }

      const storedTask = await this.state.storage.get('scheduledTask') as ScheduledTaskState;
      if (storedTask && ['checking', 'retrying', 'batch_processing', 'retry_processing'].includes(storedTask.status)) {
        this.scheduledTask = storedTask;
      }

      const storedScheduledBatchProgress = await this.state.storage.get('scheduledBatchProgress') as ScheduledBatchProgress;
      if (storedScheduledBatchProgress) {
        this.scheduledBatchProgress = storedScheduledBatchProgress;
      }
    });
  }

  async fetch(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const action = url.pathname.split('/').pop();

    switch (action) {
      case 'start-batch':
        return this.handleStartBatch(request);

      case 'progress':
        return this.handleSSEConnection(request);

      case 'status':
        return this.handleGetStatus();

      case 'cancel':
        return this.handleCancelBatch();

      case 'reset':
        return this.handleResetBatch();

      case 'start-scheduled-task':
        return this.handleStartScheduledTask();

      case 'get-task-status':
        return this.handleGetTaskStatus();

      case 'cancel-task':
        return this.handleCancelTask();

      default:
        return new Response('Not Found', { status: 404 });
    }
  }

  /**
   * 启动批量处理
   */
  private async handleStartBatch(request: Request): Promise<Response> {
    try {
      const { config } = await request.json<{ config?: Partial<BatchConfig> }>();
      
      // 检查是否已有正在进行的批处理
      if (this.currentProgress && this.currentProgress.status === 'processing') {
        return new Response(JSON.stringify({
          success: false,
          message: '已有批处理正在进行中',
          batchId: this.currentProgress.batchId
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // 更新配置
      if (config) {
        this.config = { ...this.config, ...config };
      }

      // 获取所有需要处理的账号
      const processor = new BatchProcessor(this.db);
      this.accounts = await processor.getAllAccounts();

      if (this.accounts.length === 0) {
        return new Response(JSON.stringify({
          success: false,
          message: '没有找到需要处理的账号'
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // 创建批处理进度
      const batchId = `batch_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const totalBatches = Math.ceil(this.accounts.length / this.config.batchSize);
      
      this.currentProgress = {
        batchId,
        totalAccounts: this.accounts.length,
        processedAccounts: 0,
        successCount: 0,
        failureCount: 0,
        currentBatch: 0,
        totalBatches,
        status: 'processing',
        startTime: Date.now(),
        lastUpdateTime: Date.now(),
        results: []
      };

      // 保存状态
      await this.state.storage.put('currentProgress', this.currentProgress);
      await this.state.storage.put('accounts', this.accounts);

      // 设置第一个 alarm 立即开始处理
      await this.state.storage.setAlarm(Date.now() + 100);

      return new Response(JSON.stringify({
        success: true,
        message: '批量处理已启动',
        batchId,
        totalAccounts: this.accounts.length,
        totalBatches
      }), {
        headers: { 'Content-Type': 'application/json' }
      });

    } catch (error) {
      console.error('启动批量处理失败:', error);
      return new Response(JSON.stringify({
        success: false,
        message: `启动失败: ${error}`
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }

  /**
   * 推送单个账号数据到前端
   */
  private async pushAccountDataToFrontend(
    account: { mainAccountId: number; phone: string; sessionid: string },
    accountData: any
  ): Promise<void> {
    try {
      // 获取 UserAuthDO 实例
      const id = this.env.USER_AUTH_DO.idFromName("auth");
      const userAuthDO = this.env.USER_AUTH_DO.get(id);

      // 获取完整的主账号平台数据
      const { getPlatformAccountsData } = await import('./database');
      const platformData = await getPlatformAccountsData(this.db, account.mainAccountId);

      if (!platformData.success || !platformData.accounts) {
        console.error(`获取主账号 ${account.mainAccountId} 平台数据失败，无法推送`);
        return;
      }

      const mainAccountData = platformData.accounts;
      const platformAccounts: any[] = [];

      // 构造平台账号数据
      for (const [phone, accountInfo] of Object.entries(mainAccountData)) {
        platformAccounts.push({
          ...accountInfo,
          phone  // 确保phone属性在最后，覆盖可能存在的重复属性
        });


      }

      const groupData = {
        mainAccountId: account.mainAccountId,
        platformAccounts: platformAccounts,
        totalAccounts: platformAccounts.length,
        lastUpdate: new Date().toISOString()
      };

      // 构造主账号组数据
      const mainAccountGroups = {
        [account.mainAccountId]: groupData
      };

      // 1. 推送给管理员
      const adminNotifyRequest = new Request('https://dummy.com/push-platform-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type: 'platform_data_update',
          target: 'admin',
          data: {
            mainAccountGroups,
            timestamp: new Date().toISOString(),
            singleAccount: account.phone, // 标记这是单个账号的推送
            batchUpdate: true // 标记这是批量处理中的更新
          }
        })
      });

      const adminResponse = await userAuthDO.fetch(adminNotifyRequest);

      // 2. 推送给该主账号的用户
      const userNotifyRequest = new Request('https://dummy.com/push-platform-data', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          type: 'platform_data_update',
          target: 'user',
          mainAccountId: account.mainAccountId,
          data: {
            mainAccountData: groupData,
            timestamp: new Date().toISOString(),
            singleAccount: account.phone, // 标记这是单个账号的推送
            batchUpdate: true // 标记这是批量处理中的更新
          }
        })
      });

      const userResponse = await userAuthDO.fetch(userNotifyRequest);

    } catch (error) {
      // 推送失败，静默处理
    }
  }

  /**
   * 通过管理员 SSE 推送批量处理进度
   */
  private async pushProgressToAdminSSE(progress: BatchProgress): Promise<void> {
    try {
      // 获取 UserAuthDO 实例
      const id = this.env.USER_AUTH_DO.idFromName("auth");
      const userAuthDO = this.env.USER_AUTH_DO.get(id);

      // 构造进度消息
      const progressMessage = {
        type: 'batch_progress',
        data: progress,
        timestamp: new Date().toISOString()
      };

      // 通过 UserAuthDO 推送到管理员 SSE 连接
      const request = new Request('https://dummy.com/admin/push-batch-progress', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(progressMessage)
      });

      await userAuthDO.fetch(request);
    } catch (error) {
      console.error('推送进度到管理员 SSE 失败:', error);
    }
  }

  /**
   * 处理 SSE 连接（已废弃，使用管理员 SSE 代替）
   */
  private async handleSSEConnection(request: Request): Promise<Response> {
    const url = new URL(request.url);
    const batchId = url.searchParams.get('batchId');

    if (!batchId) {
      return new Response('Missing batchId parameter', { status: 400 });
    }

    // 创建一个简单的 SSE 流，返回当前状态
    let responseText = '';

    // 发送连接确认
    responseText += 'data: {"type":"connected","message":"SSE连接已建立"}\n\n';

    // 如果有当前进度，立即发送
    if (this.currentProgress && this.currentProgress.batchId === batchId) {
      const progressData = JSON.stringify({
        type: 'progress',
        data: this.currentProgress
      });
      responseText += `data: ${progressData}\n\n`;
    } else {
      // 发送空状态
      responseText += 'data: {"type":"progress","data":null}\n\n';
    }

    return new Response(responseText, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control'
      }
    });
  }

  /**
   * 获取当前状态
   */
  private async handleGetStatus(): Promise<Response> {
    return new Response(JSON.stringify({
      success: true,
      data: this.currentProgress
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  /**
   * 取消批量处理
   */
  private async handleCancelBatch(): Promise<Response> {
    if (this.currentProgress && this.currentProgress.status === 'processing') {
      this.currentProgress.status = 'cancelled';
      this.currentProgress.lastUpdateTime = Date.now();

      await this.state.storage.put('currentProgress', this.currentProgress);
      await this.state.storage.deleteAlarm();

      return new Response(JSON.stringify({
        success: true,
        message: '批量处理已取消'
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    }

    return new Response(JSON.stringify({
      success: false,
      message: '没有正在进行的批量处理'
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  /**
   * 强制重置批量处理状态
   */
  private async handleResetBatch(): Promise<Response> {
    try {
      // 清除所有状态
      this.currentProgress = null;
      this.accounts = [];

      // 删除存储的状态
      await this.state.storage.delete('currentProgress');
      await this.state.storage.delete('accounts');

      // 删除任何设置的 alarm
      await this.state.storage.deleteAlarm();

      return new Response(JSON.stringify({
        success: true,
        message: '批量处理状态已重置'
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    } catch (error) {
      console.error('重置批量处理状态失败:', error);
      return new Response(JSON.stringify({
        success: false,
        message: `重置失败: ${error}`
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }

  /**
   * Alarm 处理器 - 处理当前批次
   */
  async alarm(): Promise<void> {
    console.log(`🚨 [Alarm] alarm触发，当前定时任务状态: ${this.scheduledTask?.status || 'null'}`);

    // 优先处理定时任务
    if (this.scheduledTask && ['checking', 'retrying', 'batch_processing', 'retry_processing'].includes(this.scheduledTask.status)) {
      console.log(`📋 [Alarm] 处理定时任务，状态: ${this.scheduledTask.status}`);
      await this.handleScheduledTaskAlarm();
      return;
    }

    // 处理批量任务
    if (!this.currentProgress || this.currentProgress.status !== 'processing') {
      return;
    }

    try {
      // 计算当前批次的账号范围
      const startIndex = this.currentProgress.currentBatch * this.config.batchSize;
      const endIndex = Math.min(startIndex + this.config.batchSize, this.accounts.length);
      const currentBatchAccounts = this.accounts.slice(startIndex, endIndex);

      // 处理当前批次的账号
      for (let i = 0; i < currentBatchAccounts.length; i++) {
        const account = currentBatchAccounts[i];

        if (this.currentProgress.status !== 'processing') {
          return;
        }

        this.currentProgress.currentAccount = account.phone;
        this.currentProgress.lastUpdateTime = Date.now();

        try {
          // 获取账号的全部数据（收益、信用分、粉丝数、账号信息、状态、阅读量、草稿箱）
          const fetchResult = await allDataFetcher(account.phone, account.sessionid);
          this.currentProgress.results.push(fetchResult);

          if (fetchResult.success && fetchResult.data) {
            // 更新数据库
            const processor = new BatchProcessor(this.db);
            const updateResult = await processor.updateDatabase(account, fetchResult.data);

            if (updateResult.success) {
              this.currentProgress.successCount++;

              // 立即推送账号数据到前端
              await this.pushAccountDataToFrontend(account, fetchResult.data);
            } else {
              this.currentProgress.failureCount++;
            }
          } else {
            this.currentProgress.failureCount++;

            // 连续失败时将账号设为掉线
            const processor = new BatchProcessor(this.db);
            await processor.setAccountOffline(account, `批量处理失败: ${fetchResult.message}`);
            console.log(`账号 ${account.phone} 已设为掉线状态`);
          }

        } catch (error) {
          this.currentProgress.failureCount++;

          // 连续失败时将账号设为掉线
          const processor = new BatchProcessor(this.db);
          await processor.setAccountOffline(account, `处理异常: ${error}`);
          console.log(`账号 ${account.phone} 已设为掉线状态（异常）`);

          this.currentProgress.results.push({
            success: false,
            phone: account.phone,
            message: `处理异常: ${error}`
          });
        }

        this.currentProgress.processedAccounts++;
        this.currentProgress.lastUpdateTime = Date.now();

        // 保存进度
        await this.state.storage.put('currentProgress', this.currentProgress);

        // 推送进度到管理员 SSE
        await this.pushProgressToAdminSSE(this.currentProgress);

        // 账号间延迟
        if (i < currentBatchAccounts.length - 1) {
          await new Promise(resolve => setTimeout(resolve, this.config.delayBetweenAccounts));
        }
      }

      // 更新批次计数
      this.currentProgress.currentBatch++;
      this.currentProgress.currentAccount = undefined;

      // 检查是否还有更多批次需要处理
      if (this.currentProgress.currentBatch < this.currentProgress.totalBatches) {
        // 设置下一个批次的 alarm
        await this.state.storage.setAlarm(Date.now() + this.config.delayBetweenBatches);
      } else {
        // 所有批次处理完成
        this.currentProgress.status = 'completed';
        this.currentProgress.lastUpdateTime = Date.now();
      }

      // 保存最终状态
      await this.state.storage.put('currentProgress', this.currentProgress);

      // 推送最终进度到管理员 SSE
      await this.pushProgressToAdminSSE(this.currentProgress);

    } catch (error) {
      console.error('批次处理失败:', error);

      if (this.currentProgress) {
        this.currentProgress.status = 'failed';
        this.currentProgress.errorMessage = String(error);
        this.currentProgress.lastUpdateTime = Date.now();
        await this.state.storage.put('currentProgress', this.currentProgress);
      }
    }
  }

  // ==================== 定时任务处理方法 ====================

  /**
   * 启动定时任务
   */
  private async handleStartScheduledTask(): Promise<Response> {
    try {
      // 检查是否已有正在进行的任务
      if (this.scheduledTask && ['checking', 'retrying', 'processing'].includes(this.scheduledTask.status)) {
        return new Response(JSON.stringify({
          success: false,
          message: `已有正在进行的任务: ${this.scheduledTask.taskId}`,
          taskId: this.scheduledTask.taskId,
          status: this.scheduledTask.status
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }

      // 获取第一个账号信息
      const processor = new BatchProcessor(this.db);
      const accounts = await processor.getAllAccounts();

      if (accounts.length === 0) {
        return new Response(JSON.stringify({
          success: false,
          message: '没有找到任何平台账号'
        }), {
          headers: { 'Content-Type': 'application/json' }
        });
      }

      const firstAccount = accounts[0];
      const taskId = `scheduled_task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

      // 创建新任务状态
      this.scheduledTask = {
        taskId,
        status: 'checking',
        startTime: Date.now(),
        lastCheckTime: Date.now(),
        retryCount: 0,
        maxRetries: 200, // 最多重试200次
        retryInterval: 3 * 60 * 1000, // 3分钟
        firstAccountPhone: firstAccount.phone,
        firstAccountSessionId: firstAccount.sessionid,
        // 智能重试相关
        currentRound: 0,
        maxRounds: 200, // 最多200轮重试
        retryIntervalMinutes: 3, // 3分钟重试间隔
        notReadyAccounts: []
      };

      // 保存状态
      await this.state.storage.put('scheduledTask', this.scheduledTask);

      // 立即开始第一次检查
      await this.state.storage.setAlarm(Date.now() + 100);

      return new Response(JSON.stringify({
        success: true,
        message: '定时任务已启动',
        taskId,
        firstAccountPhone: firstAccount.phone
      }), {
        headers: { 'Content-Type': 'application/json' }
      });

    } catch (error) {
      console.error('启动定时任务失败:', error);
      return new Response(JSON.stringify({
        success: false,
        message: `启动失败: ${error}`
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }

  /**
   * 获取定时任务状态
   */
  private async handleGetTaskStatus(): Promise<Response> {
    return new Response(JSON.stringify({
      success: true,
      task: this.scheduledTask
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  /**
   * 取消定时任务
   */
  private async handleCancelTask(): Promise<Response> {
    if (this.scheduledTask) {
      this.scheduledTask.status = 'failed';
      this.scheduledTask.errorMessage = '任务被手动取消';
      await this.state.storage.put('scheduledTask', this.scheduledTask);

      // 清除alarm（如果有的话）
      await this.state.storage.deleteAlarm();
    }

    return new Response(JSON.stringify({
      success: true,
      message: '任务已取消'
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }

  /**
   * 处理定时任务的 Alarm
   */
  private async handleScheduledTaskAlarm(): Promise<void> {
    if (!this.scheduledTask) {
      console.log(`❌ [ScheduledTaskAlarm] 没有定时任务`);
      return;
    }

    console.log(`🎯 [ScheduledTaskAlarm] 处理定时任务，状态: ${this.scheduledTask.status}`);

    try {
      switch (this.scheduledTask.status) {
        case 'checking':
        case 'retrying':
          console.log(`🔍 [ScheduledTaskAlarm] 执行第一个账号检查`);
          await this.handleFirstAccountCheck();
          break;
        case 'batch_processing':
          console.log(`🚀 [ScheduledTaskAlarm] 执行批量处理`);
          await this.handleBatchProcessing();
          break;
        case 'retry_processing':
          console.log(`🔄 [ScheduledTaskAlarm] 执行重试处理`);
          await this.handleRetryProcessing();
          break;
        default:
          console.log(`❓ [ScheduledTaskAlarm] 未知状态: ${this.scheduledTask.status}`);
      }
    } catch (error) {
      console.error('❌ [ScheduledTaskAlarm] 定时任务处理失败:', error);
      this.scheduledTask.status = 'failed';
      this.scheduledTask.errorMessage = `处理异常: ${error}`;
      await this.state.storage.put('scheduledTask', this.scheduledTask);
    }
  }

  /**
   * 检查第一个账号的收益数据
   */
  private async handleFirstAccountCheck(): Promise<void> {
    console.log(`🔍 [ScheduledTask] 开始检查第一个账号: ${this.scheduledTask!.firstAccountPhone}`);

    // 获取第一个账号的收益数据
    const incomeResult = await incomeDataFetcher(
      this.scheduledTask!.firstAccountPhone!,
      this.scheduledTask!.firstAccountSessionId!
    );

    this.scheduledTask!.lastCheckTime = Date.now();

    console.log(`📊 [ScheduledTask] 第一个账号检查结果: success=${incomeResult.success}, isYesterdayIncomeReady=${incomeResult.isYesterdayIncomeReady}`);

    if (incomeResult.success && incomeResult.isYesterdayIncomeReady) {
      // 收益数据已准备好，开始智能批量处理
      console.log(`✅ [ScheduledTask] 收益数据已准备好，开始批量处理`);
      this.scheduledTask!.status = 'batch_processing';
      this.scheduledTask!.currentRound = 1;
      await this.state.storage.put('scheduledTask', this.scheduledTask);

      // 立即开始第一轮批量处理
      console.log(`⏰ [ScheduledTask] 设置100ms后的alarm开始批量处理`);
      await this.state.storage.setAlarm(Date.now() + 100);
      console.log(`✅ [ScheduledTask] alarm设置成功`);

    } else {
      // 收益数据还未准备好，需要重试
      console.log(`⏳ [ScheduledTask] 收益数据未准备好，需要重试 (${this.scheduledTask!.retryCount + 1}/${this.scheduledTask!.maxRetries})`);
      this.scheduledTask!.retryCount++;

      if (this.scheduledTask!.retryCount >= this.scheduledTask!.maxRetries) {
        // 达到最大重试次数，将第一个账号设为掉线
        const processor = new BatchProcessor(this.db);
        const firstAccount = {
          mainAccountId: 0, // 这里需要获取真实的mainAccountId，但为了简化先用0
          phone: this.scheduledTask!.firstAccountPhone!,
          sessionid: this.scheduledTask!.firstAccountSessionId!
        };

        await processor.setAccountOffline(firstAccount, `第一个账号检查达到最大重试次数 (${this.scheduledTask!.maxRetries})`);
        console.log(`第一个账号 ${this.scheduledTask!.firstAccountPhone} 已设为掉线状态（达到最大重试次数）`);

        this.scheduledTask!.status = 'failed';
        this.scheduledTask!.errorMessage = `达到最大重试次数 (${this.scheduledTask!.maxRetries})，收益数据仍未准备好`;
        await this.state.storage.put('scheduledTask', this.scheduledTask);

        console.error(`❌ [ScheduledTask] 达到最大重试次数，任务失败`);
        return;
      }

      // 设置下次重试
      this.scheduledTask!.status = 'retrying';
      await this.state.storage.put('scheduledTask', this.scheduledTask);

      // 设置3分钟后的alarm
      console.log(`⏰ [ScheduledTask] 设置${this.scheduledTask!.retryInterval / 60000}分钟后重试`);
      await this.state.storage.setAlarm(Date.now() + this.scheduledTask!.retryInterval);
    }
  }

  /**
   * 处理第一轮批量处理
   */
  private async handleBatchProcessing(): Promise<void> {
    console.log(`🚀 [BatchProcessing] 开始智能批量处理`);

    // 初始化批量处理状态（如果还没有初始化）
    if (!this.scheduledBatchProgress) {
      const processor = new BatchProcessor(this.db);
      const accounts = await processor.getAllAccounts();

      if (accounts.length === 0) {
        this.scheduledTask!.status = 'failed';
        this.scheduledTask!.errorMessage = '没有找到任何平台账号';
        await this.state.storage.put('scheduledTask', this.scheduledTask);
        return;
      }

      this.scheduledBatchProgress = {
        accounts,
        currentAccountIndex: 0,
        totalAccounts: accounts.length,
        processedAccounts: 0,
        successCount: 0,
        failureCount: 0,
        skippedCount: 0,
        notReadyAccounts: []
      };

      // 保存批量处理状态
      await this.state.storage.put('scheduledBatchProgress', this.scheduledBatchProgress);

      console.log(`📊 [BatchProcessing] 初始化逐个账号处理: 总计${accounts.length}个账号`);
    }

    // 处理当前账号
    const accountResult = await this.processCurrentScheduledAccount();

    if (accountResult.success) {
      this.scheduledBatchProgress.currentAccountIndex++;
      this.scheduledBatchProgress.processedAccounts++;

      // 保存更新后的批量处理状态
      await this.state.storage.put('scheduledBatchProgress', this.scheduledBatchProgress);

      // 检查是否还有更多账号
      if (this.scheduledBatchProgress.currentAccountIndex < this.scheduledBatchProgress.totalAccounts) {
        console.log(`⏰ [BatchProcessing] 设置下一个账号处理 (${this.scheduledBatchProgress.currentAccountIndex + 1}/${this.scheduledBatchProgress.totalAccounts})`);
        // 设置500ms后处理下一个账号
        await this.state.storage.setAlarm(Date.now() + 500);
      } else {
        // 所有账号处理完成，检查是否需要重试
        this.scheduledTask!.notReadyAccounts = this.scheduledBatchProgress.notReadyAccounts;

        console.log(`📋 [BatchProcessing] 所有账号完成，统计: 成功${this.scheduledBatchProgress.successCount}个，失败${this.scheduledBatchProgress.failureCount}个，跳过${this.scheduledBatchProgress.skippedCount}个，未准备好${this.scheduledTask!.notReadyAccounts.length}个`);

        if (this.scheduledTask!.notReadyAccounts.length > 0) {
          console.log(`🔄 [BatchProcessing] 设置重试处理，轮次: ${this.scheduledTask!.currentRound + 1}`);
          this.scheduledTask!.status = 'retry_processing';
          this.scheduledTask!.currentRound++;

          // 清理批量处理状态，为重试做准备
          this.scheduledBatchProgress = null;
          await this.state.storage.delete('scheduledBatchProgress');

          await this.state.storage.put('scheduledTask', this.scheduledTask);

          // 设置3分钟后的alarm
          await this.state.storage.setAlarm(Date.now() + this.scheduledTask!.retryIntervalMinutes * 60 * 1000);
          console.log(`⏰ [BatchProcessing] 设置${this.scheduledTask!.retryIntervalMinutes}分钟后重试`);
        } else {
          // 所有账号都准备好了
          console.log(`✅ [BatchProcessing] 所有账号处理完成，任务结束`);
          this.scheduledTask!.status = 'completed';
          this.scheduledBatchProgress = null;
          await this.state.storage.put('scheduledTask', this.scheduledTask);
          await this.state.storage.delete('scheduledBatchProgress');
        }
      }
    } else {
      console.error(`❌ [BatchProcessing] 账号处理失败: ${accountResult.message}`);
      this.scheduledTask!.status = 'failed';
      this.scheduledTask!.errorMessage = `账号处理失败: ${accountResult.message}`;
      this.scheduledBatchProgress = null;
      await this.state.storage.put('scheduledTask', this.scheduledTask);
      await this.state.storage.delete('scheduledBatchProgress');
    }
  }

  /**
   * 处理当前定时任务的单个账号
   */
  private async processCurrentScheduledAccount(): Promise<{ success: boolean; message: string }> {
    if (!this.scheduledBatchProgress) {
      return { success: false, message: '批量处理状态未初始化' };
    }

    const currentIndex = this.scheduledBatchProgress.currentAccountIndex;
    if (currentIndex >= this.scheduledBatchProgress.accounts.length) {
      return { success: false, message: '没有更多账号需要处理' };
    }

    const account = this.scheduledBatchProgress.accounts[currentIndex];

    try {
      // 第一步：先获取收益数据，检查是否准备好
      const incomeResult = await incomeDataFetcher(account.phone, account.sessionid);

      if (!incomeResult.success) {
        this.scheduledBatchProgress!.failureCount++;

        // 收益数据获取失败时将账号设为掉线
        const processor = new BatchProcessor(this.db);
        await processor.setAccountOffline(account, `收益数据获取失败: ${incomeResult.message}`);
        console.log(`账号 ${account.phone} 已设为掉线状态（收益数据获取失败）`);

        return { success: true, message: `账号 ${account.phone} 收益数据获取失败，已设为掉线，继续下一个账号` };
      }

      const isIncomeReady = incomeResult.isYesterdayIncomeReady;

      if (!isIncomeReady) {
        // 收益数据未准备好，跳过这个账号
        this.scheduledBatchProgress!.skippedCount++;
        this.scheduledBatchProgress!.notReadyAccounts.push(account);
        return { success: true, message: `账号 ${account.phone} 收益数据未准备好，已跳过` };
      }

      // 第二步：收益数据准备好了，并行获取其他5个数据

      const [creditFansResult, accountInfoResult, accountStatusResult, readingDataResult, draftsCountResult] = await Promise.all([
        creditAndFansFetcher(account.phone, account.sessionid),
        accountInfoFetcher(account.phone, account.sessionid),
        accountStatusFetcher(account.phone, account.sessionid),
        readingDataFetcher(account.phone, account.sessionid),
        draftsCountFetcher(account.phone, account.sessionid)
      ]);

      // 第三步：合并所有数据
      const mergedData = createStandardAccountData();
      let hasAnySuccess = false;

      // 合并收益数据
      if (incomeResult.success && incomeResult.data?.stats) {
        Object.assign(mergedData.stats!, incomeResult.data.stats);
        mergedData.is_yesterday_income_ready = true; // 收益数据已准备好
        hasAnySuccess = true;
      }

      // 合并其他数据
      if (creditFansResult.success && creditFansResult.data?.stats) {
        Object.assign(mergedData.stats!, creditFansResult.data.stats);
        hasAnySuccess = true;
      }

      if (accountInfoResult.success && accountInfoResult.data) {
        Object.assign(mergedData, accountInfoResult.data);
        hasAnySuccess = true;
      }

      if (accountStatusResult.success && accountStatusResult.data) {
        Object.assign(mergedData, accountStatusResult.data);
        hasAnySuccess = true;
      }

      if (readingDataResult.success && readingDataResult.data?.stats) {
        Object.assign(mergedData.stats!, readingDataResult.data.stats);
        hasAnySuccess = true;
      }

      if (draftsCountResult.success && draftsCountResult.data) {
        Object.assign(mergedData, draftsCountResult.data);
        hasAnySuccess = true;
      }

      if (!hasAnySuccess) {
        this.scheduledBatchProgress!.failureCount++;

        // 所有数据获取都失败时将账号设为掉线
        const processor = new BatchProcessor(this.db);
        await processor.setAccountOffline(account, '所有数据获取都失败');
        console.log(`账号 ${account.phone} 已设为掉线状态（所有数据获取失败）`);

        return { success: true, message: `账号 ${account.phone} 所有数据获取都失败，已设为掉线，继续下一个账号` };
      }

      // 第四步：更新数据库
      const processor = new BatchProcessor(this.db);
      const updateResult = await processor.updateDatabase(account, mergedData);

      if (updateResult.success) {
        this.scheduledBatchProgress!.successCount++;

        // 等待100ms确保数据库写入完成
        await new Promise(resolve => setTimeout(resolve, 100));

        // 推送账号数据到前端
        await this.pushAccountDataToFrontend(account, mergedData);
      } else {
        this.scheduledBatchProgress!.failureCount++;
      }

      return { success: true, message: `账号 ${account.phone} 处理完成` };

    } catch (error) {
      this.scheduledBatchProgress!.failureCount++;

      // 处理异常时将账号设为掉线
      const processor = new BatchProcessor(this.db);
      await processor.setAccountOffline(account, `处理异常: ${error}`);
      console.log(`账号 ${account.phone} 已设为掉线状态（处理异常）`);

      return { success: true, message: `账号 ${account.phone} 处理异常，已设为掉线，继续下一个账号` };
    }
  }

  /**
   * 处理重试轮次
   */
  private async handleRetryProcessing(): Promise<void> {
    if (this.scheduledTask!.currentRound > this.scheduledTask!.maxRounds) {
      // 达到最大轮次
      this.scheduledTask!.status = 'completed';
      await this.state.storage.put('scheduledTask', this.scheduledTask);
      return;
    }

    // 重试未准备好的账号
    const retryResult = await this.retryNotReadyAccounts();

    if (retryResult.success) {
      if (this.scheduledTask!.notReadyAccounts.length > 0) {
        this.scheduledTask!.currentRound++;
        await this.state.storage.put('scheduledTask', this.scheduledTask);

        // 设置3分钟后的alarm
        await this.state.storage.setAlarm(Date.now() + this.scheduledTask!.retryIntervalMinutes * 60 * 1000);
      } else {
        // 所有账号都准备好了
        this.scheduledTask!.status = 'completed';
        await this.state.storage.put('scheduledTask', this.scheduledTask);
      }
    } else {
      this.scheduledTask!.status = 'failed';
      this.scheduledTask!.errorMessage = `重试处理失败: ${retryResult.message}`;
      await this.state.storage.put('scheduledTask', this.scheduledTask);
      console.error(`❌ [ScheduledTask] 重试处理失败: ${retryResult.message}`);
    }
  }

  /**
   * 批量更新账号数据（避免并发问题）
   */
  private async batchUpdateAccountsData(
    mainAccountId: number,
    accountsData: { [phone: string]: any }
  ): Promise<{ success: boolean; message: string }> {
    try {
      // 获取现有数据
      const result = await this.db.prepare(`
        SELECT platform_accounts_data FROM main_account_platform_data WHERE main_account_id = ?
      `).bind(mainAccountId).first<{ platform_accounts_data: string }>();

      if (!result) {
        return { success: false, message: "未找到平台账号数据" };
      }

      // 解析现有数据
      let platformData;
      try {
        platformData = JSON.parse(result.platform_accounts_data);
      } catch (error) {
        return { success: false, message: "数据格式错误" };
      }

      const now = new Date().toISOString();

      // 批量更新所有账号数据
      for (const [phone, updateData] of Object.entries(accountsData)) {
        if (platformData.accounts[phone]) {
          const currentAccount = platformData.accounts[phone];

          // 合并更新数据
          platformData.accounts[phone] = {
            ...currentAccount,
            ...updateData,
            updated_at: now
          };

          // 特别处理 stats 对象的合并更新
          if (updateData.stats && currentAccount.stats) {
            platformData.accounts[phone].stats = {
              ...currentAccount.stats,
              ...updateData.stats
            };
          }
        }
      }

      platformData.metadata.last_batch_update = now;

      // 一次性写入数据库
      const updateResult = await this.db.prepare(`
        UPDATE main_account_platform_data
        SET platform_accounts_data = ?, updated_at = ?
        WHERE main_account_id = ?
      `).bind(JSON.stringify(platformData), now, mainAccountId).run();

      if (!updateResult.success) {
        console.error(`批量数据库更新失败 (mainAccountId: ${mainAccountId}):`, updateResult.error);
        return { success: false, message: `数据库更新失败: ${updateResult.error || '未知错误'}` };
      }

      return {
        success: true,
        message: `成功批量更新 ${Object.keys(accountsData).length} 个账号`
      };
    } catch (error) {
      console.error("批量更新账号数据失败:", error);
      return { success: false, message: `批量更新失败: ${error}` };
    }
  }

  /**
   * 智能批量更新所有账号数据
   * 只有收益数据真正准备好的账号才设置 is_yesterday_income_ready = true
   */
  private async smartBatchUpdateAllData(): Promise<{ success: boolean; message: string }> {
    try {
      const processor = new BatchProcessor(this.db);
      const accounts = await processor.getAllAccounts();

      if (accounts.length === 0) {
        return { success: false, message: '没有找到任何平台账号' };
      }

      let successCount = 0;
      let failureCount = 0;
      const notReadyAccounts: Array<{ mainAccountId: number; phone: string; sessionid: string }> = [];

      // 分批处理账号（4个一组）
      const batchSize = 4;
      for (let i = 0; i < accounts.length; i += batchSize) {
        const batch = accounts.slice(i, i + batchSize);

        // 并行处理当前批次的账号
        const batchPromises = batch.map(async (account) => {
          try {
            // 获取账号数据
            const fetchResult = await allDataFetcher(account.phone, account.sessionid);

            if (fetchResult.success && fetchResult.data) {
              // 检查收益数据是否准备好
              const isIncomeReady = fetchResult.isYesterdayIncomeReady;

              // 关键逻辑：只有收益数据真正准备好的账号才设置 is_yesterday_income_ready = true
              if (!isIncomeReady) {
                // 收益数据未准备好，修改数据中的状态
                fetchResult.data.is_yesterday_income_ready = false;
                notReadyAccounts.push(account);
              } else {
                // 收益数据已准备好
                fetchResult.data.is_yesterday_income_ready = true;
              }

              // 更新数据库
              const updateResult = await processor.updateDatabase(account, fetchResult.data);
              if (updateResult.success) {
                successCount++;
              } else {
                failureCount++;
              }
            } else {
              failureCount++;
            }
          } catch (error) {
            failureCount++;
          }
        });

        // 等待当前批次完成
        await Promise.all(batchPromises);

        // 批次间延迟
        if (i + batchSize < accounts.length) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      // 更新未准备好的账号列表
      this.scheduledTask!.notReadyAccounts = notReadyAccounts;

      const message = `智能批量处理完成: 总计${accounts.length}个账号，成功${successCount}个，失败${failureCount}个，收益数据未准备好${notReadyAccounts.length}个`;

      return { success: true, message };

    } catch (error) {
      console.error('智能批量处理失败:', error);
      return { success: false, message: `智能批量处理失败: ${error}` };
    }
  }

  /**
   * 重试未准备好的账号
   */
  private async retryNotReadyAccounts(): Promise<{ success: boolean; message: string }> {
    try {
      if (!this.scheduledTask!.notReadyAccounts || this.scheduledTask!.notReadyAccounts.length === 0) {
        return { success: true, message: '没有需要重试的账号' };
      }

      const processor = new BatchProcessor(this.db);
      const accountsToRetry = [...this.scheduledTask!.notReadyAccounts];

      let successCount = 0;
      let failureCount = 0;
      const stillNotReadyAccounts: Array<{ mainAccountId: number; phone: string; sessionid: string }> = [];

      // 分批处理账号（4个一组）
      const batchSize = 4;
      for (let i = 0; i < accountsToRetry.length; i += batchSize) {
        const batch = accountsToRetry.slice(i, i + batchSize);

        // 并行处理当前批次的账号
        const batchPromises = batch.map(async (account) => {
          try {
            // 获取账号数据
            const fetchResult = await allDataFetcher(account.phone, account.sessionid);

            if (fetchResult.success && fetchResult.data) {
              // 检查收益数据是否准备好
              const isIncomeReady = fetchResult.isYesterdayIncomeReady;

              // 关键逻辑：只有收益数据真正准备好的账号才设置 is_yesterday_income_ready = true
              if (!isIncomeReady) {
                // 收益数据仍未准备好
                fetchResult.data.is_yesterday_income_ready = false;
                stillNotReadyAccounts.push(account);
              } else {
                // 收益数据现在准备好了
                fetchResult.data.is_yesterday_income_ready = true;
              }

              // 更新数据库
              const updateResult = await processor.updateDatabase(account, fetchResult.data);
              if (updateResult.success) {
                successCount++;
              } else {
                failureCount++;
              }
            } else {
              failureCount++;

              // 检查是否已经重试太多次，如果是则设为掉线
              if (this.scheduledTask!.currentRound >= 10) { // 重试10轮后设为掉线
                await processor.setAccountOffline(account, `重试${this.scheduledTask!.currentRound}轮后仍然失败`);
                console.log(`账号 ${account.phone} 重试${this.scheduledTask!.currentRound}轮后仍失败，已设为掉线状态`);
              } else {
                stillNotReadyAccounts.push(account); // 获取失败的账号继续重试
              }
            }
          } catch (error) {
            failureCount++;

            // 检查是否已经重试太多次，如果是则设为掉线
            if (this.scheduledTask!.currentRound >= 10) { // 重试10轮后设为掉线
              await processor.setAccountOffline(account, `重试${this.scheduledTask!.currentRound}轮后仍异常: ${error}`);
              console.log(`账号 ${account.phone} 重试${this.scheduledTask!.currentRound}轮后仍异常，已设为掉线状态`);
            } else {
              stillNotReadyAccounts.push(account); // 异常的账号继续重试
            }
          }
        });

        // 等待当前批次完成
        await Promise.all(batchPromises);

        // 批次间延迟
        if (i + batchSize < accountsToRetry.length) {
          await new Promise(resolve => setTimeout(resolve, 500));
        }
      }

      // 更新仍未准备好的账号列表
      this.scheduledTask!.notReadyAccounts = stillNotReadyAccounts;

      const message = `重试处理完成: 重试${accountsToRetry.length}个账号，成功${successCount}个，失败${failureCount}个，仍未准备好${stillNotReadyAccounts.length}个`;

      return { success: true, message };

    } catch (error) {
      console.error('重试处理失败:', error);
      return { success: false, message: `重试处理失败: ${error}` };
    }
  }
}
