# SSE推送详细说明

## 概述

Server-Sent Events (SSE) 是一种服务器向客户端推送实时数据的技术。本系统使用SSE实现多端数据同步、状态通知等功能。

## SSE连接

### 连接建立

**端点**: `GET /auth/sse`

**参数**:
- `sessionId`: 用户会话ID
- `clientId`: 客户端唯一标识

**连接URL示例**:
```
GET /auth/sse?sessionId=session_abc123&clientId=client_xyz789
```

### 前端连接代码
```javascript
function connectSSE() {
  if (sseConnection && sseConnection.readyState === EventSource.OPEN) {
    return; // 已经连接
  }
  
  if (!sessionId) return;
  
  const clientId = localStorage.getItem('clientId') || generateClientId();
  localStorage.setItem('clientId', clientId);
  
  const sseUrl = `${API_URL}/auth/sse?sessionId=${sessionId}&clientId=${clientId}`;
  
  sseConnection = new EventSource(sseUrl);
  
  sseConnection.onopen = () => {
    console.log('SSE连接已建立');
  };
  
  sseConnection.onmessage = (event) => {
    try {
      const data = JSON.parse(event.data);
      handleSSEMessage(data);
    } catch (error) {
      console.error('处理SSE消息时出错:', error);
    }
  };
  
  sseConnection.onerror = (error) => {
    console.error('SSE错误:', error);
    // EventSource会自动重连，无需手动处理
  };
}

function generateClientId() {
  return 'client_' + Math.random().toString(36).substring(2, 15);
}
```

## 推送消息类型

### 1. 连接确认 (connected)

**触发时机**: SSE连接建立成功时

**消息格式**:
```json
{
  "type": "connected",
  "message": "SSE连接已建立"
}
```

**前端处理**:
```javascript
case 'connected':
  console.log('SSE连接确认');
  break;
```

### 2. 强制登出 (force_logout)

**触发时机**: 
- 用户在其他设备强制登录
- 管理员强制用户下线
- 会话安全检查失败

**消息格式**:
```json
{
  "type": "force_logout",
  "message": "您的账号在其他设备登录，已被强制下线"
}
```

**前端处理**:
```javascript
case 'force_logout':
  showMessage(data.message, 'error');
  handleLogout(false); // 不发送退出请求，直接清理本地状态
  break;
```

### 3. 子账号添加平台账号通知 (sub_account_platform_added)

**触发时机**: 子账号添加新的平台账号时，通知主账号

**消息格式**:
```json
{
  "type": "sub_account_platform_added",
  "message": "子账号 *********** 添加了新的平台账号 ***********",
  "data": {
    "subAccountPhone": "***********",
    "platformPhone": "***********",
    "platform": "头条号",
    "contentType": "视频"
  }
}
```

**前端处理**:
```javascript
case 'sub_account_platform_added':
  showMessage(data.message, 'info');
  setTimeout(() => {
    loadPlatformAccounts(); // 刷新平台账号列表
  }, 1000);
  break;
```

### 4. 平台账号转移/添加接收通知 (platform_account_received)

**触发时机**: 
- 接收到其他用户转移的平台账号
- 管理员为用户添加平台账号

**消息格式**:
```json
{
  "type": "platform_account_received",
  "message": "您收到了来自 *********** 转移的平台账号 ***********",
  "data": {
    "fromUserPhone": "***********",
    "platformPhone": "***********",
    "platform": "头条号",
    "addedByUserType": "用户", // 或 "管理员"
    "transferType": "transfer" // 或 "admin_add"
  }
}
```

**前端处理**:
```javascript
case 'platform_account_received':
  const messageType = data.data && data.data.addedByUserType === '管理员' ? 'info' : 'success';
  showMessage(data.message, messageType);
  setTimeout(() => {
    loadPlatformAccounts();
  }, 1000);
  break;
```

### 5. 批量平台账号转移通知 (platform_accounts_batch_received)

**触发时机**: 接收到批量转移的平台账号

**消息格式**:
```json
{
  "type": "platform_accounts_batch_received",
  "message": "您收到了来自 *********** 的 3 个平台账号批量转移",
  "data": {
    "fromUserPhone": "***********",
    "count": 3,
    "operatorType": "用户", // 或 "管理员"
    "platformPhones": ["***********", "***********", "***********"]
  }
}
```

**前端处理**:
```javascript
case 'platform_accounts_batch_received':
  const batchMessageType = data.data && data.data.operatorType === '管理员' ? 'info' : 'success';
  showMessage(data.message, batchMessageType);
  setTimeout(() => {
    loadPlatformAccounts();
  }, 1000);
  break;
```

### 6. 平台数据更新通知 (platform_data_update)

**触发时机**: 
- DO+Alarms系统推送平台数据更新
- 平台账号收益、粉丝等数据发生变化

**消息格式**:
```json
{
  "type": "platform_data_update",
  "message": "平台数据已更新",
  "data": {
    "updateType": "stats", // 更新类型：stats, status, info
    "platformPhone": "***********",
    "platform": "头条号",
    "updatedFields": ["total_income", "yesterday_income", "followers"]
  }
}
```

**前端处理**:
```javascript
case 'platform_data_update':
  // 立即刷新平台账号数据，不显示消息（避免频繁提示）
  loadPlatformAccounts();
  break;
```

### 7. 平台账号删除通知 (platform_account_deleted_notification)

**触发时机**: 
- 平台账号被删除时通知相关用户
- 管理员删除平台账号

**消息格式**:
```json
{
  "type": "platform_account_deleted_notification",
  "message": "平台账号 *********** 已被删除",
  "data": {
    "platformPhone": "***********",
    "platform": "头条号",
    "deletedByUserType": "用户", // 或 "管理员"
    "deletedByUserPhone": "***********",
    "isCurrentUser": false, // 是否为当前用户操作
    "reason": "用户主动删除" // 删除原因
  }
}
```

**前端处理**:
```javascript
case 'platform_account_deleted_notification':
  let deleteMessageType = 'warning';
  if (data.data && data.data.deletedByUserType === '管理员') {
    deleteMessageType = 'info';
  } else if (data.data && data.data.isCurrentUser) {
    deleteMessageType = 'success'; // 自己删除的显示为成功
  }
  showMessage(data.message, deleteMessageType);
  setTimeout(() => {
    loadPlatformAccounts();
  }, 1000);
  break;
```

## 连接管理

### 连接状态检查
```javascript
function checkSSEConnection() {
  if (!sseConnection) return 'disconnected';
  
  switch (sseConnection.readyState) {
    case EventSource.CONNECTING:
      return 'connecting';
    case EventSource.OPEN:
      return 'connected';
    case EventSource.CLOSED:
      return 'disconnected';
    default:
      return 'unknown';
  }
}
```

### 手动重连
```javascript
function reconnectSSE() {
  if (sseConnection) {
    sseConnection.close();
    sseConnection = null;
  }
  
  setTimeout(() => {
    connectSSE();
  }, 1000); // 延迟1秒重连
}
```

### 关闭连接
```javascript
function disconnectSSE() {
  if (sseConnection) {
    sseConnection.close();
    sseConnection = null;
  }
}
```

## 错误处理

### 连接错误处理
```javascript
sseConnection.onerror = (error) => {
  console.error('SSE连接错误:', error);
  
  // 检查连接状态
  if (sseConnection.readyState === EventSource.CLOSED) {
    console.log('SSE连接已关闭，将自动重连');
  }
  
  // EventSource会自动重连，但可以添加额外的错误处理逻辑
  // 例如：显示连接状态、记录错误日志等
};
```

### 消息解析错误处理
```javascript
sseConnection.onmessage = (event) => {
  try {
    const data = JSON.parse(event.data);
    handleSSEMessage(data);
  } catch (error) {
    console.error('SSE消息解析错误:', error);
    console.error('原始消息:', event.data);
    // 可以发送错误报告到服务器
  }
};
```

## 最佳实践

### 1. 连接管理
- 登录成功后立即建立SSE连接
- 退出登录时主动关闭连接
- 页面刷新时检查并恢复连接

### 2. 消息处理
- 统一的消息处理函数
- 根据消息类型执行不同的业务逻辑
- 避免重复处理相同消息

### 3. 用户体验
- 连接状态指示器
- 消息通知样式区分
- 适当的延迟刷新避免频繁更新

### 4. 性能优化
- 避免在SSE消息处理中执行耗时操作
- 合理使用setTimeout延迟执行
- 批量处理相似消息

### 5. 错误恢复
- 自动重连机制
- 连接失败时的降级方案
- 错误日志记录

## 调试技巧

### 1. 连接状态监控
```javascript
setInterval(() => {
  console.log('SSE连接状态:', checkSSEConnection());
}, 10000); // 每10秒检查一次
```

### 2. 消息日志
```javascript
function handleSSEMessage(data) {
  console.log('收到SSE消息:', data);
  
  // 原有的消息处理逻辑
  switch (data.type) {
    // ...
  }
}
```

### 3. 网络状态监控
```javascript
window.addEventListener('online', () => {
  console.log('网络已连接，检查SSE状态');
  if (checkSSEConnection() === 'disconnected') {
    connectSSE();
  }
});

window.addEventListener('offline', () => {
  console.log('网络已断开');
});
```

## 安全考虑

1. **身份验证**: 所有SSE连接都需要有效的sessionId
2. **权限控制**: 只推送用户有权限接收的消息
3. **消息加密**: 敏感信息在传输前加密
4. **频率限制**: 防止消息推送过于频繁
5. **连接限制**: 限制单用户的并发连接数

---

*SSE推送说明版本: v1.0*  
*最后更新: 2024-08-01*
