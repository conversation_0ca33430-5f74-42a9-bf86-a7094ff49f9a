# 演示页面功能说明文档

## 概述

本文档详细说明了用户登录系统演示页面（`yanshiye.html`）的功能实现，包括平台账号管理、SSE实时推送、用户认证等核心功能的API接口和数据格式。

## 目录

1. [用户认证系统](#用户认证系统)
2. [平台账号管理](#平台账号管理)
3. [用户界面操作指南](#用户界面操作指南)
4. [SSE实时推送](#sse实时推送)
5. [子账号管理](#子账号管理)
6. [激活码系统](#激活码系统)
7. [权限控制系统](#权限控制系统)
8. [数据格式说明](#数据格式说明)

---

## 用户认证系统

### 登录接口

**端点**: `POST /auth/login`

**请求参数**:
```json
{
  "phone": "***********",
  "password": "password123",
  "clientVersion": "1.0.0",
  "forceLogin": false
}
```

**响应数据**:
```json
{
  "success": true,
  "sessionId": "session_abc123",
  "user": {
    "id": 123,
    "phone": "***********",
    "account_type": "主账号",
    "owner_phone": null,
    "expiry_date": "2024-12-31 23:59:59",
    "sub_accounts": []
  },
  "requireForceLogin": false,
  "needUpgrade": false,
  "latestVersion": "1.0.0"
}
```

**特殊情况处理**:
- `requireForceLogin: true` - 需要强制登录确认
- `needUpgrade: true` - 客户端版本过低，需要升级

### 注册接口

**端点**: `POST /auth/register`

**请求参数**:
```json
{
  "phone": "***********",
  "password": "password123"
}
```

**响应数据**:
```json
{
  "success": true,
  "message": "注册成功"
}
```

### 会话检查

**端点**: `POST /auth/check-session`

**请求参数**:
```json
{
  "sessionId": "session_abc123"
}
```

**响应数据**:
```json
{
  "success": true,
  "userId": 123,
  "phone": "***********"
}
```

### 退出登录

**端点**: `POST /auth/logout`

**请求参数**:
```json
{
  "sessionId": "session_abc123"
}
```

---

## 平台账号管理

### 添加平台账号

**端点**: `POST /api/user/platform-accounts`

**请求参数**:
```json
{
  "account_data": {
    "phone": "***********",
    "platform": "头条号",
    "login_type": "视频",
    "team_tag": "-",
    "data_update_time": "2024-08-01 10:30:00",
    "login_time": "2024-08-01 10:30:00",
    "username": "-",
    "sessionid": "platform_session_123",
    "homepage_url": "-",
    "is_verified": "否",
    "drafts_count": "-",
    "stats": {
      "followers": "-",
      "total_reads": "-",
      "total_income": "-",
      "yesterday_reads": "-",
      "yesterday_income": "-",
      "credit_score": "-",
      "can_withdraw_amount": "-"
    },
    "account_status": "正常"
  },
  "owner_id": 123,
  "current_holder_id": 123,
  "sessionId": "user_session_abc123"
}
```

**字段说明**:
- `platform`: 平台类型，可选值：`头条号`、`百家号`
- `login_type`: 内容类型，可选值：`视频`、`文章`、`微头条`
- `sessionid`: 平台SessionID，用于平台认证
- `account_status`: 账户状态，可选值：`正常`、`掉线`、`异常`

**响应数据**:
```json
{
  "success": true,
  "message": "平台账号添加成功，收益数据正在获取中..."
}
```

### 获取平台账号列表

**端点**: `GET /api/user/platform-accounts`

**请求参数**:
- `user_id`: 用户ID
- `is_main_account`: 是否为主账号（true/false）
- `sessionId`: 会话ID

**响应数据**:
```json
{
  "success": true,
  "accountInfos": [
    {
      "phone": "***********",
      "platform": "头条号",
      "username": "测试用户",
      "login_type": "视频",
      "team_tag": "A组",
      "is_verified": "是",
      "account_status": "正常",
      "login_time": "2024-08-01 10:30:00",
      "current_holder_id": 123,
      "owner_id": 123,
      "drafts_count": "5",
      "sessionid": "platform_session_123",
      "homepage_url": "https://example.com",
      "data_update_time": "2024-08-01 11:00:00",
      "stats": {
        "followers": "10000",
        "credit_score": "95",
        "total_income": "1500.50",
        "yesterday_income": "25.30",
        "can_withdraw_amount": "1200.00",
        "total_reads": "500000",
        "yesterday_reads": "2500"
      }
    }
  ]
}
```

### 编辑平台账号

**端点**: `PUT /api/user/platform-accounts`

**功能说明**:
编辑平台账号功能允许用户修改已添加的平台账号信息。该功能具有严格的权限控制，确保数据安全。

**权限要求**:
- **主账号**: 可以编辑所有平台账号（包括自己添加的和子账号添加的）
- **子账号**: 只能编辑归属于自己的平台账号（current_holder_id 等于自己的用户ID）

**可编辑字段**:
- `platform`: 平台类型（头条号、百家号）
- `login_type`: 内容类型（视频、文章、微头条）
- `sessionid`: 平台SessionID
- `team_tag`: 团队标签

**不可编辑字段**:
- `phone`: 平台手机号（作为唯一标识符，不允许修改）

**请求参数**:
```json
{
  "phone": "***********",
  "userId": 123,
  "is_main_account": true,
  "accountData": {
    "platform": "头条号",
    "login_type": "视频",
    "team_tag": "A组",
    "sessionid": "new_session_id_123",
    "updated_at": "2024-08-01T12:00:00.000Z"
  },
  "sessionId": "user_session_abc123"
}
```

**响应数据**:
```json
{
  "success": true,
  "message": "平台账号更新成功"
}
```

**错误响应示例**:
```json
{
  "success": false,
  "message": "您没有权限编辑此平台账号"
}
```

### 删除平台账号

**端点**: `DELETE /api/user/platform-accounts`

**请求参数**:
```json
{
  "phone": "***********",
  "userId": 123,
  "sessionId": "user_session_abc123"
}
```

### 转移平台账号

**端点**: `POST /api/user/transfer-platform-account`

**请求参数**:
```json
{
  "phone": "***********",
  "new_holder_id": 456,
  "user_id": 123,
  "sessionId": "user_session_abc123"
}
```

### 批量转移平台账号

**端点**: `POST /api/user/batch-transfer-platform-accounts`

**请求参数**:
```json
{
  "user_id": 123,
  "transfers": [
    {
      "phone": "***********",
      "newHolderId": 456
    },
    {
      "phone": "***********",
      "newHolderId": 456
    }
  ],
  "sessionId": "user_session_abc123"
}
```

**响应数据**:
```json
{
  "success": true,
  "successCount": 2,
  "failCount": 0,
  "results": [
    {
      "phone": "***********",
      "success": true
    },
    {
      "phone": "***********",
      "success": true
    }
  ]
}
```

---

## 用户界面操作指南

### 页面布局结构

演示页面采用响应式布局，主要分为以下几个区域：

#### 1. 顶部用户信息栏
- **用户基本信息**: 显示手机号、账号类型（主账号/子账号）
- **状态指示器**: 登录状态、激活状态
- **归属信息**: 子账号显示归属的主账号
- **到期时间**: 已激活用户显示会员到期时间
- **操作按钮**: 退出登录按钮

#### 2. 左侧功能面板
- **激活账户区域**: 输入激活码进行账户激活（仅主账号可见）
- **子账号管理区域**: 添加、查看、删除子账号（仅主账号可见）

#### 3. 右侧主要内容区域
- **平台账号管理表格**: 显示所有平台账号信息
- **搜索和筛选控件**: 按平台手机号搜索
- **批量操作控件**: 全选、清空选择、批量转移

### 平台账号管理操作

#### 添加平台账号
1. 点击"添加平台账号"按钮
2. 在弹出的模态框中填写以下信息：
   - **平台手机号**: 必填，平台账号的手机号
   - **平台**: 必选，选择头条号或百家号
   - **内容类型**: 必选，选择视频、文章或微头条
   - **SessionID**: 必填，平台的会话标识
   - **团队标签**: 可选，用于分组管理
3. 点击"添加"按钮完成添加
4. 系统会自动开始获取平台收益数据

#### 编辑平台账号
1. 在平台账号表格中找到要编辑的账号
2. 点击该行的"编辑"按钮
3. 在弹出的编辑模态框中：
   - **平台手机号**: 显示为灰色，不可修改
   - **其他字段**: 可以修改平台、内容类型、SessionID、团队标签
4. 修改完成后点击"添加"按钮保存更改
5. 系统会实时更新显示的信息

**编辑权限说明**:
- **主账号**: 可以编辑所有平台账号
- **子账号**: 只能编辑归属于自己的平台账号

#### 删除平台账号
1. 在平台账号表格中找到要删除的账号
2. 点击该行的"删除"按钮
3. 在确认对话框中点击"确定"
4. 系统会立即删除该平台账号

**删除权限说明**:
- **主账号**: 可以删除所有平台账号
- **子账号**: 只能删除自己添加的平台账号

#### 转移平台账号
1. 在平台账号表格中找到要转移的账号
2. 点击该行的"转移"按钮（仅主账号可见）
3. 在转移模态框中选择新的归属用户：
   - 可以选择主账号本身
   - 可以选择任意子账号
4. 点击"确认转移"完成操作

#### 批量转移平台账号
1. 使用复选框选择要转移的多个平台账号
2. 点击"批量转移"按钮（仅主账号可见）
3. 在批量转移模态框中：
   - 查看已选择的平台账号列表
   - 选择新的归属用户
4. 点击"确认批量转移"完成操作

#### 搜索和筛选
1. 在搜索框中输入平台手机号
2. 点击"搜索"按钮或按回车键
3. 表格会实时显示匹配的结果
4. 清空搜索框可恢复显示所有账号

#### 批量选择操作
- **全选**: 点击"全选"按钮选择当前页面所有账号
- **清空选择**: 点击"清空选择"按钮取消所有选择
- **单个选择**: 点击每行前面的复选框进行单个选择
- **表头复选框**: 显示当前选择状态（全选/部分选择/未选择）

### 实时数据更新

系统通过SSE（Server-Sent Events）技术实现实时数据推送：

1. **自动刷新**: 当平台数据更新时，表格会自动刷新显示最新数据
2. **通知提醒**: 收到平台账号转移、删除等操作时会显示通知消息
3. **状态同步**: 多个设备登录同一账号时，操作会实时同步

### 响应式设计

页面支持不同屏幕尺寸：

- **桌面端**: 完整显示所有功能和列
- **平板端**: 适当调整表格列宽和按钮大小
- **移动端**: 表格可横向滚动，操作按钮适配触摸操作

---

## SSE实时推送

### 连接建立

**端点**: `GET /auth/sse`

**请求参数**:
- `sessionId`: 会话ID
- `clientId`: 客户端唯一标识

**连接URL示例**:
```
GET /auth/sse?sessionId=session_abc123&clientId=client_xyz789
```

### 推送消息类型

#### 1. 连接确认
```json
{
  "type": "connected",
  "message": "SSE连接已建立"
}
```

#### 2. 强制登出通知
```json
{
  "type": "force_logout",
  "message": "您的账号在其他设备登录，已被强制下线"
}
```

#### 3. 子账号添加平台账号通知
```json
{
  "type": "sub_account_platform_added",
  "message": "子账号 *********** 添加了新的平台账号",
  "data": {
    "subAccountPhone": "***********",
    "platformPhone": "***********"
  }
}
```

#### 4. 平台账号转移/添加接收通知
```json
{
  "type": "platform_account_received",
  "message": "您收到了来自 *********** 转移的平台账号 ***********",
  "data": {
    "fromUserPhone": "***********",
    "platformPhone": "***********",
    "addedByUserType": "用户"
  }
}
```

#### 5. 批量平台账号转移通知
```json
{
  "type": "platform_accounts_batch_received",
  "message": "您收到了 3 个平台账号的批量转移",
  "data": {
    "count": 3,
    "operatorType": "用户",
    "fromUserPhone": "***********"
  }
}
```

#### 6. 平台数据更新通知
```json
{
  "type": "platform_data_update",
  "message": "平台数据已更新",
  "data": {
    "updateType": "stats",
    "platformPhone": "***********"
  }
}
```

#### 7. 平台账号删除通知
```json
{
  "type": "platform_account_deleted_notification",
  "message": "平台账号 *********** 已被删除",
  "data": {
    "platformPhone": "***********",
    "deletedByUserType": "用户",
    "isCurrentUser": false
  }
}
```

---

## 子账号管理

### 获取子账号列表

**端点**: `GET /api/user/sub-accounts`

**请求参数**:
- `ownerId`: 主账号ID
- `sessionId`: 会话ID

**响应数据**:
```json
{
  "success": true,
  "subAccounts": [
    {
      "id": 456,
      "phone": "***********",
      "created_at": "2024-07-01 10:00:00",
      "last_login_at": "2024-08-01 09:30:00"
    }
  ]
}
```

### 创建子账号

**端点**: `POST /api/user/create-sub-account`

**请求参数**:
```json
{
  "ownerId": 123,
  "phone": "***********",
  "password": "password123",
  "sessionId": "user_session_abc123"
}
```

### 删除子账号

**端点**: `POST /api/user/delete-sub-account`

**请求参数**:
```json
{
  "ownerId": 123,
  "subAccountId": 456,
  "sessionId": "user_session_abc123"
}
```

---

## 激活码系统

### 激活码使用

**端点**: `POST /auth/activate-code`

**请求参数**:
```json
{
  "sessionId": "user_session_abc123",
  "code": "ACTIVATE123"
}
```

**响应数据**:
```json
{
  "success": true,
  "days": 30,
  "message": "激活成功！已添加30天会员"
}
```

---

## 权限控制系统

### 账号类型说明

系统支持两种账号类型，具有不同的权限级别：

#### 主账号
- **完整管理权限**: 可以管理所有功能和数据
- **子账号管理**: 可以创建、删除子账号
- **平台账号管理**: 可以添加、编辑、删除、转移所有平台账号
- **激活功能**: 可以使用激活码激活账户
- **数据查看**: 可以查看所有平台账号的数据

#### 子账号
- **受限管理权限**: 只能管理自己的数据
- **平台账号管理**: 只能添加、编辑、删除归属于自己的平台账号
- **无转移权限**: 不能转移平台账号
- **无子账号管理**: 不能创建或删除子账号
- **无激活功能**: 不能使用激活码

### 平台账号权限控制

#### 编辑权限
```javascript
// 权限检查逻辑
const canEdit = (currentUser, platformAccount) => {
  // 主账号可以编辑所有账号
  if (currentUser.account_type === '主账号') {
    return true;
  }

  // 子账号只能编辑归属于自己的账号
  return platformAccount.current_holder_id === currentUser.id;
};
```

#### 删除权限
```javascript
// 删除权限检查
const canDelete = (currentUser, platformAccount) => {
  // 主账号可以删除所有账号
  if (currentUser.account_type === '主账号') {
    return true;
  }

  // 子账号只能删除自己添加的账号
  return platformAccount.owner_id === currentUser.id;
};
```

#### 转移权限
```javascript
// 转移权限检查
const canTransfer = (currentUser) => {
  // 只有主账号可以转移平台账号
  return currentUser.account_type === '主账号';
};
```

### 数据访问控制

#### 平台账号数据获取
- **主账号**: 获取所有平台账号数据（包括子账号添加的）
- **子账号**: 只获取归属于自己的平台账号数据

#### API权限验证
所有API请求都会进行以下验证：
1. **会话验证**: 检查sessionId的有效性
2. **用户身份验证**: 确认用户身份和账号类型
3. **操作权限验证**: 根据账号类型检查操作权限
4. **数据归属验证**: 确保用户只能操作有权限的数据

### 前端权限控制

#### 界面元素显示控制
```javascript
// 根据用户类型显示/隐藏功能
if (currentUser.account_type === '主账号') {
  // 显示子账号管理区域
  subAccountManagement.classList.remove('hidden');
  // 显示激活功能
  activationArea.classList.remove('hidden');
  // 显示转移按钮
  transferButtons.forEach(btn => btn.style.display = 'inline-block');
} else {
  // 隐藏主账号专用功能
  subAccountManagement.classList.add('hidden');
  activationArea.classList.add('hidden');
  transferButtons.forEach(btn => btn.style.display = 'none');
}
```

#### 操作按钮权限控制
```javascript
// 编辑按钮权限控制
const showEditButton = (currentUser, platformAccount) => {
  if (currentUser.account_type === '主账号') {
    return true;
  }
  return platformAccount.current_holder_id === currentUser.id;
};

// 删除按钮权限控制
const showDeleteButton = (currentUser, platformAccount) => {
  if (currentUser.account_type === '主账号') {
    return true;
  }
  return platformAccount.owner_id === currentUser.id;
};
```

### 安全措施

#### 1. 会话管理
- 会话ID具有时效性，定期自动过期
- 强制登录功能，防止多设备同时登录
- 退出登录时立即清理会话信息

#### 2. 数据验证
- 前端和后端双重验证
- 参数类型和格式验证
- SQL注入防护

#### 3. 权限验证
- 每个API请求都进行权限验证
- 数据库层面的权限控制
- 操作日志记录

#### 4. 错误处理
- 权限不足时返回明确的错误信息
- 不暴露敏感的系统信息
- 统一的错误响应格式

---

## 数据格式说明

### 用户信息结构
```json
{
  "id": 123,
  "phone": "***********",
  "account_type": "主账号",
  "owner_phone": null,
  "expiry_date": "2024-12-31 23:59:59",
  "sub_accounts": []
}
```

### 平台账号统计数据结构
```json
{
  "followers": "10000",
  "credit_score": "95",
  "total_income": "1500.50",
  "yesterday_income": "25.30",
  "can_withdraw_amount": "1200.00",
  "total_reads": "500000",
  "yesterday_reads": "2500"
}
```

### 错误响应格式
```json
{
  "success": false,
  "message": "错误描述信息",
  "code": "ERROR_CODE"
}
```

---

## 前端实现要点

### 1. 会话管理
- **存储机制**: 使用localStorage存储sessionId，确保页面刷新后保持登录状态
- **自动检查**: 页面加载时调用`checkSession()`自动验证会话有效性
- **失效处理**: 会话失效时自动清理本地存储并跳转到登录页面
- **强制登录**: 支持强制登录功能，处理多设备登录冲突

### 2. SSE连接管理
- **自动建立**: 登录成功后立即建立SSE连接，获取实时推送
- **重连机制**: 连接断开时自动重连，确保数据同步
- **优雅关闭**: 退出登录时主动关闭SSE连接，释放资源
- **错误处理**: 连接失败时提供用户友好的错误提示

### 3. 数据刷新策略
- **延迟刷新**: SSE推送触发的数据刷新延迟1秒执行，避免频繁更新
- **即时刷新**: 用户操作（添加、编辑、删除）后立即刷新相关数据
- **本地筛选**: 搜索和筛选基于本地缓存数据进行，提高响应速度
- **智能更新**: 只更新变化的数据，减少不必要的DOM操作

### 4. 权限控制实现
- **动态界面**: 根据用户类型动态显示/隐藏功能模块
- **按钮控制**: 编辑、删除、转移按钮根据权限动态显示
- **操作验证**: 前端操作前进行权限预检查，提供即时反馈
- **错误处理**: 权限不足时显示明确的错误信息

### 5. 平台账号编辑功能实现
- **模态框复用**: 编辑和添加共用同一个模态框，通过状态区分
- **表单预填充**: 编辑模式下自动填充现有数据
- **字段控制**: 编辑模式下禁用手机号字段，防止误修改
- **状态管理**: 使用全局变量`currentEditingPhone`标识编辑状态
- **数据验证**: 提交前验证必填字段和数据格式

### 6. 批量操作实现
- **选择状态管理**: 使用Set数据结构管理选中的账号
- **全选逻辑**: 支持全选、部分选择、清空选择的状态切换
- **批量转移**: 支持多账号同时转移，提供操作进度反馈
- **错误处理**: 批量操作失败时显示详细的成功/失败统计

### 7. 用户体验优化
- **响应式设计**: 适配不同屏幕尺寸，确保移动端可用性
- **加载状态**: 操作过程中显示加载指示器
- **操作反馈**: 成功/失败操作提供明确的视觉反馈
- **表单验证**: 实时验证用户输入，提供即时错误提示
- **模态框管理**: 支持ESC键关闭、外部点击关闭等交互方式

### 8. 性能优化
- **数据缓存**: 本地缓存平台账号数据，减少API调用
- **虚拟滚动**: 大量数据时考虑使用虚拟滚动技术
- **防抖处理**: 搜索输入使用防抖，避免频繁API调用
- **资源管理**: 及时清理事件监听器和定时器，防止内存泄漏

---

## 注意事项

### 安全相关
1. **会话安全**: 所有API请求都需要携带有效的sessionId，确保用户身份验证
2. **权限验证**: 前端和后端都要进行权限验证，防止越权操作
3. **数据保护**: 敏感信息（如SessionID）要妥善保护，避免泄露
4. **输入验证**: 对用户输入进行严格验证，防止XSS和注入攻击

### 数据一致性
1. **实时同步**: SSE推送确保多端数据同步，避免数据不一致
2. **操作确认**: 重要操作（删除、转移）需要用户确认，防止误操作
3. **事务处理**: 批量操作使用事务处理，确保数据完整性
4. **冲突处理**: 并发操作时要处理数据冲突，提供合理的解决方案

### 错误处理
1. **分类处理**: 网络错误、业务错误、权限错误分别处理
2. **用户友好**: 错误信息要用户友好，避免技术术语
3. **日志记录**: 重要错误要记录日志，便于问题排查
4. **降级方案**: 关键功能失效时提供降级方案

### 性能优化
1. **数据分页**: 大量数据时使用分页或虚拟滚动，避免页面卡顿
2. **缓存策略**: 合理使用缓存，减少不必要的API调用
3. **资源管理**: 及时清理不用的资源，防止内存泄漏
4. **网络优化**: 合并请求、压缩数据，提高网络传输效率

### 兼容性
1. **浏览器兼容**: 确保SSE在不同浏览器中的兼容性
2. **移动端适配**: 确保移动端的触摸操作和显示效果
3. **网络环境**: 考虑弱网络环境下的用户体验
4. **版本兼容**: API版本变更时要保持向后兼容

### 用户体验
1. **操作反馈**: 所有用户操作都要有明确的反馈
2. **加载状态**: 长时间操作要显示加载状态
3. **操作指引**: 复杂操作提供操作指引或帮助文档
4. **快捷操作**: 提供快捷键和批量操作，提高操作效率

### 开发维护
1. **代码规范**: 遵循代码规范，保持代码可读性
2. **文档更新**: 功能变更时及时更新文档
3. **测试覆盖**: 重要功能要有充分的测试覆盖
4. **监控告警**: 关键指标要有监控和告警机制

---

## 更新日志

### v2.0 (2024-08-01)
- 新增用户界面操作指南章节，详细说明各功能的操作步骤
- 完善平台账号编辑功能说明，包括权限控制和操作限制
- 新增权限控制系统章节，详细说明主账号和子账号的权限差异
- 优化前端实现要点，增加编辑功能、批量操作等实现细节
- 完善注意事项，增加安全、性能、兼容性等方面的指导

### v1.0 (2024-07-01)
- 初始版本，包含基本的API接口说明
- 用户认证系统、平台账号管理、SSE推送等核心功能文档

---

*文档版本: v2.0*
*最后更新: 2024-08-01*
